//go:build azure

package model

import (
	"github.com/Microsoft/cognitive-services-speech-sdk-go/audio"
	"github.com/Microsoft/cognitive-services-speech-sdk-go/speech"
)

// AzureRecogFileReq 包含 Azure 語音識別文件請求的相關配置
// 只有在編譯時包含 azure tag 時才會包含實際的 Azure SDK 類型
type AzureRecogFileReq struct {
	AudioCfg         *audio.AudioConfig
	SpeechCfg        *speech.SpeechConfig
	SpeechRecognizer *speech.SpeechRecognizer
	SessionID        chan string
	WebHook          string
}
