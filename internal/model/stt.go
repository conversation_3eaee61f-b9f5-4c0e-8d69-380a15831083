package model

import (
	"context"
	"time"
)

type SttStartReq struct {
	Action          string `json:"action"`
	ShortCommand    bool   `json:"short_command"`
	EnablePartial   bool   `json:"enable_partial"`
	CallID          string `json:"call_id"`
	VccID           string `json:"vccid"`
	RouteAccessCode string `json:"route_access_code"`
	Role            string `json:"role"`
	Params          string `json:"params"`
}

type SttStopReq struct {
	Action string `json:"action"`
}

type SttActionAck struct {
	Message struct {
		Type string `json:"type"`
	} `json:"message"`
	Ack    string `json:"ack"`
	Status int    `json:"status"`
}

type SttResult struct {
	Message struct {
		Type string `json:"type"`
	} `json:"message"`
	Status int  `json:"status"`
	Final  bool `json:"final"`
	Result struct {
		Likelihood float32 `json:"likelihood"`
		Transcript string  `json:"transcript"`
	} `json:"result"`
}

type VoiceBuffer struct {
	Ctx    context.Context
	Buffer []byte
}

type AckRes struct {
	Message struct {
		Type string `json:"type"`
	} `json:"message"`
	Ack    string `json:"ack"`
	Status int    `json:"status"`
}

type ResultRes struct {
	Message struct {
		Type string `json:"type"`
	} `json:"message"`
	Status int  `json:"status"`
	Final  bool `json:"final"`
	Result struct {
		Likelihood float32 `json:"likelihood"`
		Transcript string  `json:"transcript"`
	} `json:"result"`
}
type WavHeader struct {
	ChunkID       [4]byte
	ChunkSize     uint32
	Format        [4]byte
	Subchunk1ID   [4]byte
	Subchunk1Size uint32
	AudioFormat   uint16
	NumChannels   uint16
	SampleRate    uint32
	ByteRate      uint32
	BlockAlign    uint16
	BitsPerSample uint16
	Subchunk2ID   [4]byte
	Subchunk2Size uint32
}

type RecogFileResult struct {
	Error    error
	Text     string
	Duration time.Duration
	Offset   time.Duration
}

type ShortCommand struct {
	Text string
}
type STTParams struct {
	Vccid           string
	CallID          string
	RouteAccessCode string
	FileName        string
	WebHook         string
	WaitResult      bool
	ResKey          string
	EndPointID      string
	Region          string
	Translate       bool
}
