//go:build azure

package azure

import (
	"SonaMesh/internal/service"
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/text/gstr"

	. "SonaMesh/internal/consts"
	"SonaMesh/internal/model"

	"github.com/Microsoft/cognitive-services-speech-sdk-go/audio"
	"github.com/Microsoft/cognitive-services-speech-sdk-go/speech"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/os/gtimer"
	"github.com/gogf/gf/v2/util/gconv"
)

type STT struct {
	ackFunc           AckFunc
	resultFunc        ResultFunc
	audioConfig       *audio.AudioConfig
	speechConfig      *speech.SpeechConfig
	speechRecognizer  *speech.SpeechRecognizer
	stream            *audio.PushAudioInputStream
	ctx               context.Context
	receivedTime      *gtime.Time
	isReceivedTimeout bool
	recogReq          *model.AzureRecogFileReq
	workerPool        *grpool.Pool
	isShortCommand    bool
	chShortCommand    chan *model.ShortCommand
	isInterrupt       bool
	profileKey        string
	startTime         *gtime.Time
	usedKey           string
}

func NewAzureSTT(ackHandler AckFunc, resultHandler ResultFunc, profileKey string) (*STT, error) {

	o := &STT{
		ackFunc:    ackHandler,
		resultFunc: resultHandler,
		workerPool: grpool.New(),
	}
	if g.IsEmpty(profileKey) {
		o.profileKey = `vendor.Azure.STT`
	} else {
		o.profileKey = fmt.Sprintf(CfgKeyAzureSTT, profileKey)
	}

	v, err := g.Cfg().Get(context.TODO(), o.profileKey)
	if err != nil {
		return nil, err
	}
	if v.IsEmpty() {
		return nil, gerror.Newf("Please check  the configuration , key %q ", o.profileKey)
	}

	return o, nil
}

func (az *STT) Connect(ctx context.Context) (err error) {
	g.Log().Cat(INFO).Cat(AZ).Info(ctx, "Connect to host")
	key, region := service.GetAzureEndPointKeyAndRegion(ctx, az.profileKey)
	//vKey, _ := g.Cfg().Get(ctx, az.profileKey+".key", "")
	//vRegion, _ := g.Cfg().Get(ctx, az.profileKey+".region", "")
	vParams, _ := g.Cfg().Get(ctx, az.profileKey+".params", "")

	if g.IsEmpty(key) || g.IsEmpty(region) {
		g.Log().Cat(ERROR).Error(ctx, "The key ,region is not retrieved... ")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}
	az.usedKey = key
	g.Log().Cat(DEBUG).Debugf(ctx, "Select region -> %v , key ->[%v]", region, key)

	var jsParams *gjson.Json
	jsParams, err = gjson.LoadContent([]byte(vParams.String()))
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}
	fnWrapError := func(e error) error {
		g.Log().Cat(ERROR).Error(ctx, e)
		return gerror.WrapCode(ErrorGeneralError, e)
	}

	az.stream, err = audio.CreatePushAudioInputStream()
	if err != nil {
		return fnWrapError(err)
	}

	//if useDefault := jsParams.Get("type.useDefault", true); useDefault.Bool() {
	//
	//} else {
	//	samplesPerSecond := jsParams.Get("type.samplesPerSecond", 16000).Uint32()
	//	bitsPerSample := jsParams.Get("type.bitsPerSample", 16).Uint8()
	//	channel := jsParams.Get("type.channels", 1).Uint8()
	//	waveFormat := audio.WaveMULAW
	//	switch gstr.ToLower(jsParams.Get("type.waveFormat", WaveMULAW).String()) {
	//	case WavePCM:
	//		waveFormat = audio.WavePCM
	//	case WaveALAW:
	//		waveFormat = audio.WaveALAW
	//	}
	//
	//	_ = waveFormat
	//
	//	// wavFmt, err := audio.GetWaveFormat(samplesPerSecond, bitsPerSample, channel, waveFormat)
	//	wavFmt, err := audio.GetWaveFormatPCM(samplesPerSecond, bitsPerSample, channel)
	//	if err != nil {
	//		return fnWrapError(err)
	//	}
	//
	//	az.stream, err = audio.CreatePushAudioInputStreamFromFormat(wavFmt)
	//	if err != nil {
	//		return fnWrapError(err)
	//	}
	//
	//}

	az.audioConfig, err = audio.NewAudioConfigFromStreamInput(az.stream)
	if err != nil {
		return fnWrapError(err)
	}
	az.speechConfig, err = speech.NewSpeechConfigFromSubscription(key, region)
	if err != nil {
		return fnWrapError(err)
	}
	err = az.speechConfig.SetSpeechRecognitionLanguage(jsParams.Get("language", "zh-TW").String())

	return
}

func (az *STT) Start(ctx context.Context, params any) (err error) {
	g.Log().Cat(INFO).Cat(AZ).Infof(ctx, "Azure_STT_Start: %+v", params)
	var startReq *model.SttStartReq
	_ = gconv.Scan(params, &startReq)

	fnReturnStartAck := func(statusCode int) {
		ack := &model.AckRes{
			Message: struct {
				Type string `json:"type"`
			}(struct{ Type string }{Type: "ack"}),
			Ack:    ActionStart,
			Status: statusCode,
		}
		if az.ackFunc != nil {
			az.ackFunc(ctx, ack)
		}
	}
	fnLogErrorAndTriggerAck := func(e error) error {
		g.Log().Cat(ERROR).Error(ctx, e)
		errorCpy := e
		if gerror.Code(e) != ErrorGeneralError {
			errorCpy = gerror.WrapCode(ErrorGeneralError, e)
		}
		fnReturnStartAck(ErrorGeneralError.Code())

		return errorCpy
	}

	if az.speechConfig == nil || az.audioConfig == nil {
		err = gerror.NewCode(ErrorGeneralError, "Azure related parameters are not initialized")
		return fnLogErrorAndTriggerAck(err)
	}
	az.speechRecognizer, err = speech.NewSpeechRecognizerFromConfig(az.speechConfig, az.audioConfig)
	if err != nil {
		return fnLogErrorAndTriggerAck(err)
	}
	az.isShortCommand = startReq.ShortCommand
	if startReq.ShortCommand {
		az.chShortCommand = make(chan *model.ShortCommand, 1)
	}
	az.speechRecognizer.SessionStarted(az.sessionStart)
	az.speechRecognizer.SessionStopped(az.sessionStopped)
	az.speechRecognizer.Recognized(az.recognized)
	az.speechRecognizer.Recognizing(az.recognizing)
	az.speechRecognizer.Canceled(az.canceled)
	az.startTime = gtime.Now()
	az.speechRecognizer.StartContinuousRecognitionAsync()
	az.ctx = ctx

	// If normal startup starts, identification will be triggered from session start.
	// onsessionstop sends stop ack and uninitialized
	return
}

func (az *STT) checkRecvTimeout(ctx context.Context) {
	if az.receivedTime != nil {
		if az.stream == nil {
			gtimer.Exit()
			return
		}

		vTimeout, _ := g.Cfg().Get(ctx, az.profileKey+".recognized_timeout", "10s")
		if gtime.Now().Sub(az.receivedTime) >= vTimeout.Duration() {
			az.isReceivedTimeout = true
			g.Log().Cat(DEBUG).Cat(AZ).Debug(ctx, "Receive timeout ... ")
			// Receiving the recognition result times out and returns finish.
			ack := &model.AckRes{
				Message: struct {
					Type string `json:"type"`
				}(struct{ Type string }{Type: "ack"}),
				Ack:    ActionFinish,
				Status: ErrorState.Code(),
			}

			_ = az.Stop(ctx, `{"action":"stop"}`)

			if az.ackFunc != nil {
				az.ackFunc(ctx, ack)
			}
			gtimer.Exit()

		}
	}
}

func (az *STT) sessionStart(event speech.SessionEventArgs) {
	mu := sync.Mutex{}
	mu.Lock()
	defer func() {
		event.Close()
		mu.Unlock()
	}()

	g.Log().Cat(DEBUG).Cat(AZ).Debugf(az.ctx, "Session start : ID=%s", event.SessionID)
	// send start ack
	ack := &model.AckRes{
		Message: struct {
			Type string `json:"type"`
		}(struct{ Type string }{Type: "ack"}),
		Ack:    ActionStart,
		Status: 0,
	}
	if az.ackFunc != nil {
		az.ackFunc(az.ctx, ack)
	}

	if az.isShortCommand {
		_ = az.workerPool.Add(az.ctx, az.waitShortCommandRecog)
	} else {
		// Check if timeout has not yet been entered and recognized
		az.receivedTime = gtime.Now()
		gtimer.SetInterval(az.ctx, time.Second, az.checkRecvTimeout)
	}
}

func (az *STT) waitShortCommandRecog(ctx context.Context) {
	g.Log().Cat(INFO).Cat(AZ).Info(ctx, "Wait recognition of short command ...")
	vTimeout, _ := g.Cfg().Get(ctx, CfgKeySTT+".short_command_recog_timeout", "5s")
	defer func() {
		if az.speechRecognizer != nil {
			az.speechRecognizer.Close()
			az.speechRecognizer = nil
		}
	}()
	fnFinish := func() {
		ack := &model.AckRes{}
		ack.Message.Type = "ack"
		ack.Status = ErrorState.Code()
		ack.Ack = ActionFinish
		if az.ackFunc != nil {
			az.ackFunc(ctx, ack)
		}
	}

	fnResult := func(r string) {
		result := &model.ResultRes{}
		result.Message.Type = "result"
		if g.IsEmpty(r) {
			result.Result.Likelihood = 0.0
		} else {
			result.Result.Likelihood = 1.0
		}
		result.Result.Transcript = r
		result.Final = true
		result.Status = ErrorOK.Code()
		if az.resultFunc != nil {
			az.resultFunc(ctx, result)
		}
	}
	var r *model.ShortCommand
	g.Log().Cat(DEBUG).Cat(AZ).Debug(ctx, "Wait recognize result ... ")

	select {
	case r = <-az.chShortCommand:
	case <-time.After(vTimeout.Duration()):
		g.Log().Cat(DEBUG).Cat(AZ).Debug(ctx, "Identify short command timeout")
		fnFinish()
		if az.speechRecognizer != nil {
			g.Log().Cat(DEBUG).Cat(AZ).Debug(ctx, "Stop recognition of short command ... ")
			az.speechRecognizer.StopContinuousRecognitionAsync()
		}
		return

	}
	g.Log().Cat(DEBUG).Cat(AZ).Debugf(ctx, "Recognized Results Of Short Instructions:%s", r.Text)
	fnResult(r.Text)

	if az.speechRecognizer != nil {
		g.Log().Cat(DEBUG).Cat(AZ).Debug(ctx, "Stop recognition of short command ... ")
		az.speechRecognizer.StopContinuousRecognitionAsync()
	}
}

// do not enable this feature
//func (az *STT) SyncRecogShortCommand(ctx context.Context) {
//	g.Log().Cat(INFO).Cat(AZ).Info(ctx, "Synchronous identification of short commands")
//	fnFinishAck := func() {
//		var ack = &model.AckRes{
//			Message: struct {
//				Type string `json:"type"`
//			}(struct{ Type string }{Type: "ack"}),
//			Ack:    ActionFinish,
//			Status: ErrorState.Code(),
//		}
//		if az.ackFunc != nil {
//			az.ackFunc(az.ctx, ack)
//		}
//	}
//	fnResultAck := func(text string) {
//		var ack = &model.ResultRes{}
//		ack.Result.Likelihood = 1.0
//		ack.Result.Transcript = text
//		ack.Final = true
//		ack.Status = ErrorOK.Code()
//		ack.Message.Type = StateResult
//		if az.resultFunc != nil {
//			az.resultFunc(ctx, ack)
//		}
//
//	}
//	var outcome speech.SpeechRecognitionOutcome
//	vTimeout, _ := g.Cfg().Get(ctx, CfgKeySTT+"short_command_recog_timeout", "5s")
//	task := az.speechRecognizer.RecognizeOnceAsync()
//	select {
//	case outcome = <-task:
//	case <-time.After(vTimeout.Duration()):
//		g.Log().Cat(DEBUG).Cat(AZ).Debug(ctx, "Short command recognition timeout...")
//		// if timeout occurs finish ack is returned
//		fnFinishAck()
//		return
//	}
//	if outcome.Error != nil {
//		g.Log().Cat(ERROR).Error(ctx, outcome.Error)
//		fnFinishAck()
//		return
//	} else {
//		//  return recognition results
//		fnResultAck(outcome.Result.Text)
//
//	}
//
//}

func (az *STT) sessionStopped(event speech.SessionEventArgs) {
	mu := sync.Mutex{}
	mu.Lock()

	defer func() {
		event.Close()
		mu.Unlock()
	}()

	g.Log().Cat(DEBUG).Cat(AZ).Debugf(az.ctx, "Session stop : ID=%s", event.SessionID)
	fnStop := func() {
		ack := &model.AckRes{
			Message: struct {
				Type string `json:"type"`
			}(struct{ Type string }{Type: "ack"}),
			Ack:    ActionStop,
			Status: 0,
		}

		if az.ackFunc != nil {
			az.ackFunc(az.ctx, ack)
		}
	}

	if az.isReceivedTimeout == false && az.isShortCommand == false && az.isInterrupt == false {
		fnStop()
	}

	defer func() {
		az.stream, az.audioConfig, az.speechConfig, az.speechRecognizer = nil, nil, nil, nil
		az.isReceivedTimeout = false
		az.isShortCommand = false
		az.isInterrupt = false
	}()

	defer func() {
		if az.stream != nil {
			az.stream.Close()
		}
		if az.audioConfig != nil {
			az.audioConfig.Close()
		}
		if az.speechConfig != nil {
			az.speechConfig.Close()
		}
		if az.speechRecognizer != nil {
			az.speechRecognizer.Close()
		}

	}()

	if az.isShortCommand {
		select {
		case _, ok := <-az.chShortCommand:
			if !ok {
				g.Log().Cat(DEBUG).Cat(AZ).Debug(az.ctx, "Short command channel is closed  ")
			}

		default:
			g.Log().Cat(DEBUG).Cat(AZ).Debug(az.ctx, "Recognition has ended stop waiting")
			az.chShortCommand <- &model.ShortCommand{Text: ""}
		}

		fnStop()

	}
}

func (az *STT) recognized(event speech.SpeechRecognitionEventArgs) {
	mu := sync.Mutex{}
	mu.Lock()
	defer func() {
		event.Close()
		mu.Unlock()
	}()
	g.Log().Cat(DEBUG).Cat(AZ).Debugf(az.ctx, "Recognized : ID=%s Text=%s ", event.SessionID, event.Result.Text)

	dur := gtime.Now().Sub(az.startTime)
	service.SetNodeCost(az.ctx, az.profileKey, az.usedKey, gconv.Int(dur.Seconds()))
	az.startTime = gtime.Now()

	if az.isShortCommand == false && az.isInterrupt == false {

		// update received time
		az.receivedTime = gtime.Now()
		// trigger recognition results
		result := &model.ResultRes{}
		result.Message.Type = "result"
		result.Result.Likelihood = 1.0
		result.Final = true
		result.Status = ErrorOK.Code()
		result.Result.Transcript = event.Result.Text
		if az.resultFunc != nil {
			az.resultFunc(az.ctx, result)
		}

	} else {
		az.chShortCommand <- &model.ShortCommand{Text: event.Result.Text}
	}

}

func (az *STT) recognizing(event speech.SpeechRecognitionEventArgs) {
	defer event.Close()
	// In the process of intermediate identification, knowledge is logged
	// g.Log().Cat(DEBUG).Cat(AZ).Debugf(az.ctx, "Recognizing : ID=%s Text=%s ", event.SessionID, event.Result.Text)
}

func (az *STT) canceled(event speech.SpeechRecognitionCanceledEventArgs) {
	defer event.Close()
	// cancel recognition currently only log
	g.Log().Cat(DEBUG).Cat(AZ).Debugf(az.ctx, "Cancelled : ID=%s", event.SessionID)
}

func (az *STT) Stop(ctx context.Context, params any) (err error) {
	mu := sync.Mutex{}
	mu.Lock()
	defer mu.Unlock()

	g.Log().Cat(INFO).Cat(AZ).Infof(ctx, "Azure_STT_Stop: %+v", params)
	fnStop := func() {
		ack := &model.AckRes{
			Message: struct {
				Type string `json:"type"`
			}(struct{ Type string }{Type: "ack"}),
			Ack:    ActionStop,
			Status: 0,
		}
		if az.ackFunc != nil {
			az.ackFunc(az.ctx, ack)
		}
	}

	if az.audioConfig == nil || az.speechConfig == nil || az.speechRecognizer == nil || az.stream == nil {
		err = gerror.NewCode(ErrorGeneralError, "Not start recognition  or recognition is over ... ")
		g.Log().Cat(DEBUG).Cat(AZ).Debug(ctx, err)
		fnStop()
		return
	}

	az.speechRecognizer.StopContinuousRecognitionAsync()
	// the finish response is sent in session stop
	return
}

func (az *STT) SendVoiceBuffer(ctx context.Context, buf []byte) (err error) {
	// g.Log().Cat(INFO).Cat(AZ).Infof(ctx, "Azure_STT_VocBuf: length -> %d", len(buf))
	if az.stream == nil {
		err = gerror.NewCode(ErrorGeneralError, "The stream is not initialized ")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}
	err = az.stream.Write(buf)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
	}

	return
}

// RecognizeFile performs speech-to-text recognition on an audio file using Azure Cognitive Services.
//
// Context: The context to carry deadlines, cancellations, and other request-scoped values.
// file (string): The path to the audio file to be recognized.
//
// Returns:
//   res (any): The result of speech-to-text recognition.
//     - string: The recognized text.
//   err (error): An error, if any, encountered during the execution of the method.

func (az *STT) RecognizeFile(ctx context.Context, params *model.STTParams) (res any, err error) {
	g.Log().Cat(INFO).Cat(AZ).Infof(ctx, "Recognize file: %v", gjson.New(params).MustToJsonString())

	//vKey, _ := g.Cfg().Get(ctx, az.profileKey+".key", "")
	//vRegion, _ := g.Cfg().Get(ctx, az.profileKey+".region", "")

	key, reg := service.GetAzureEndPointKeyAndRegion(ctx, az.profileKey)

	vParams, _ := g.Cfg().Get(ctx, az.profileKey+".params", "")
	if g.IsEmpty(key) || g.IsEmpty(reg) {
		g.Log().Cat(ERROR).Error(ctx, "Azure Cognitive Services related parameters have not been set")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}

	fnLogError := func(e error) error {
		g.Log().Cat(ERROR).Error(ctx, e)
		return gerror.WrapCode(ErrorGeneralError, e)
	}
	audioConfig, err := audio.NewAudioConfigFromWavFileInput(params.FileName)
	if err != nil {
		err = fnLogError(err)
		return
	}
	defer audioConfig.Close()
	speechKey := key
	region := reg

	if !g.IsEmpty(gstr.Trim(params.ResKey)) && !g.IsEmpty(gstr.Trim(params.EndPointID)) {
		speechKey = params.ResKey
	}
	if !g.IsEmpty(gstr.Trim(params.Region)) {
		region = params.Region
	}

	speechConfig, err := speech.NewSpeechConfigFromSubscription(speechKey, region)
	if err != nil {
		err = fnLogError(err)
		return
	}
	defer speechConfig.Close()

	var jsParams *gjson.Json
	jsParams, err = gjson.LoadContent([]byte(vParams.String()))
	if err != nil {
		err = fnLogError(err)
		return
	}
	err = speechConfig.SetSpeechRecognitionLanguage(jsParams.Get("language", "zh-tw").String())
	if err != nil {
		err = fnLogError(err)
		return
	}

	if !g.IsEmpty(params.EndPointID) {
		err = speechConfig.SetEndpointID(params.EndPointID)
		if err != nil {
			err = fnLogError(err)
			return
		}
	}
	speechRecognizer, err := speech.NewSpeechRecognizerFromConfig(speechConfig, audioConfig)
	if err != nil {
		err = fnLogError(err)
		return
	}
	defer speechRecognizer.Close()

	az.recogReq = &model.AzureRecogFileReq{
		AudioCfg:         audioConfig,
		SpeechCfg:        speechConfig,
		SpeechRecognizer: speechRecognizer,
		SessionID:        make(chan string, 1),
		WebHook:          params.WebHook,
	}

	vTimeout, _ := g.Cfg().Get(ctx, CfgKeySTT+".action_ack_timeout", "1m")
	az.ctx = gctx.NeverDone(ctx)
	az.usedKey = key
	_ = az.workerPool.AddWithRecover(az.ctx, func(ctx context.Context) {
		bstop := make(chan bool, 1)
		sbContent := strings.Builder{}

		defer func() {
			az.recogReq.SpeechRecognizer.StopContinuousRecognitionAsync()
			az.recogReq.SpeechRecognizer.Close()
			az.recogReq.SpeechCfg.Close()
			az.recogReq.AudioCfg.Close()
		}()

		az.recogReq.SpeechRecognizer.SessionStarted(func(event speech.SessionEventArgs) {
			defer event.Close()
			g.Log().Cat(DEBUG).Cat(AZ).Debugf(ctx, "session started : ID = %s", event.SessionID)
			if !params.WaitResult {
				az.recogReq.SessionID <- event.SessionID
			}

		})

		az.recogReq.SpeechRecognizer.SessionStopped(func(event speech.SessionEventArgs) {
			defer event.Close()
			g.Log().Cat(DEBUG).Cat(AZ).Debugf(ctx, "session stopped : ID = %s", event.SessionID)
			if params.WaitResult {
				az.recogReq.SessionID <- sbContent.String()
			}
			bstop <- true
		})
		az.recogReq.SpeechRecognizer.Recognized(func(event speech.SpeechRecognitionEventArgs) {
			defer event.Close()
			defer func() {
				dur := gtime.Now().Sub(az.startTime)
				service.SetNodeCost(az.ctx, az.profileKey, az.usedKey, gconv.Int(dur.Seconds()))
				az.startTime = gtime.Now()
			}()

			g.Log().Cat(DEBUG).Cat(AZ).Debugf(
				ctx,
				"recognized : text [%s] , duration:[%v], offset:[%v]",
				event.Result.Text,
				event.Result.Duration.String(),
				event.Result.Offset,
			)

			if !params.WaitResult && !g.IsEmpty(az.recogReq.WebHook) {
				e := gerror.NewCode(ErrorOK)
				g.Client().ContentJson().PostContent(ctx, az.recogReq.WebHook, g.Map{
					"code":       gerror.Code(e).Code(),
					"message":    gerror.Code(e).Message(),
					"text":       event.Result.Text,
					"session_id": event.SessionID,
				})

			} else {
				sbContent.WriteString(event.Result.Text)
			}
		})
		az.startTime = gtime.Now()
		az.recogReq.SpeechRecognizer.StartContinuousRecognitionAsync()

		select {
		case <-bstop:
			return
		}
	}, func(ctx context.Context, exception error) {
		g.Log().Cat(ERROR).Cat(AZ).Error(ctx, exception)
		az.recogReq.SessionID <- ""
	})
	select {
	case res = <-az.recogReq.SessionID:
		return
	case <-time.After(vTimeout.Duration()):
		err = gerror.NewCode(ErrorGeneralError, "Timeout ...")
		return nil, err
	}
}

func (az *STT) Interrupt(ctx context.Context) {
	g.Log().Cat(INFO).Cat(AZ).Info(ctx, "Azure_STT_Interrupt: ... ")
	az.isInterrupt = true
	if az.speechRecognizer != nil {
		az.speechRecognizer.StopContinuousRecognitionAsync()
	}
}
