//go:build !azure

package azure

import (
	"context"
	"fmt"

	. "SonaMesh/internal/consts"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// TTS 的 stub 實現，當不包含 azure tag 時使用
type TTS struct {
	profileKey string
}

// NewAzureTTS 創建 Azure TTS 的 stub 實現
func NewAzureTTS(profileKey string) (*TTS, error) {
	o := &TTS{}
	if g.IsEmpty(profileKey) {
		o.profileKey = `vendor.Azure.TTS`
	} else {
		o.profileKey = fmt.Sprintf(CfgKeyAzureTTS, profileKey)
	}

	// 在 stub 實現中，我們不檢查配置，因為 Azure 功能不可用
	g.Log().Cat(INFO).Infof(context.TODO(), "Azure TTS is not supported in this build (compiled without azure tag)")

	return o, nil
}

// Connect 的 stub 實現
func (az *TTS) Connect(ctx context.Context) (err error) {
	// TTS Connect 在原始實現中也是空的，所以這裡也返回 nil
	return nil
}

// Synthesis 的 stub 實現
func (az *TTS) Synthesis(ctx context.Context, params any) (res any, err error) {
	err = gerror.NewCode(ErrorGeneralError, "Azure TTS is not supported in this build")
	g.Log().Cat(ERROR).Error(ctx, err)
	return nil, err
}
