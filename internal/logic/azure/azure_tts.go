//go:build azure

package azure

import (
	"SonaMesh/internal/service"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/os/gcache"
	"time"

	. "SonaMesh/internal/consts"
	"SonaMesh/internal/model"

	"github.com/Microsoft/cognitive-services-speech-sdk-go/audio"
	"github.com/Microsoft/cognitive-services-speech-sdk-go/common"
	"github.com/Microsoft/cognitive-services-speech-sdk-go/speech"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
)

type TTS struct {
	profileKey string
}

func NewAzureTTS(profileKey string) (*TTS, error) {

	o := &TTS{}
	if g.<PERSON>(profileKey) {
		o.profileKey = `vendor.Azure.TTS`
	} else {
		o.profileKey = fmt.Sprintf(CfgKeyAzureTTS, profileKey)
	}
	v, err := g.Cfg().Get(context.TODO(), o.profileKey)

	if err != nil {
		return nil, err
	}
	if v.IsEmpty() {
		return nil, gerror.Newf("Please check  the configuration , key %q ", o.profileKey)
	}

	return o, nil
}

func (az *TTS) Connect(ctx context.Context) (err error) {
	return
}

// Synthesis performs text-to-speech synthesis using Azure Cognitive Services.
//
// Context: The context to carry deadlines, cancellations, and other request-scoped values.
// params (any): The input parameters for text-to-speech synthesis.
//     - Type (string): The audio type, e.g., "wav" or "mp3".
//     - Speed (string): The speech speed.
//     - Volume (string): The speech volume.
//     - Text (string): The text to be synthesized.
//
// Returns:
//   res (any): The result of text-to-speech synthesis.
//     - Buf ([]byte): The audio data.
//     - FileName (string): The name of the audio file.
//     - Ext (string): The extension of the audio file.
//   err (error): An error, if any, encountered during the execution of the method.

func (az *TTS) Synthesis(ctx context.Context, params any) (res any, err error) {

	//vKey, _ := g.Cfg().Get(ctx, az.profileKey+".key", "")
	//vRegion, _ := g.Cfg().Get(ctx, az.profileKey+".region", "")

	key, region := service.GetAzureEndPointKeyAndRegion(ctx, az.profileKey)
	vPattern, _ := g.Cfg().Get(ctx, az.profileKey+".pattern", "")

	if g.IsEmpty(key) || g.IsEmpty(region) || vPattern.IsEmpty() {
		g.Log().Cat(ERROR).Error(ctx, "Azure Cognitive Services related parameters have not been set")
		err = gerror.NewCode(ErrorParamsError, "key or  region is empty")

		return
	}
	g.Log().Cat(DEBUG).Debugf(ctx, "Select region -> %v , key ->[%v]", region, key)
	var req *model.TtsReq
	ret := &model.TtsRes{}
	_ = gconv.Scan(params, &req)
	if req == nil {
		err = gerror.NewCode(ErrorGeneralError, "Failed to convert parameters to tts request")
		g.Log().Cat(ERROR).Error(ctx, err)
		return
	}
	vParams, _ := g.Cfg().Get(ctx, az.profileKey+".params")
	var jsParams *gjson.Json
	jsParams, err = gjson.LoadContent([]byte(vParams.String()))
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}
	fnType := func() string {
		if !g.IsEmpty(req.Type) {

			if gstr.ToLower(req.Type) != "wav" && gstr.ToLower(req.Type) != "mp3" {
				return jsParams.Get("type", "wav").String()
			}

			return req.Type
		}
		return jsParams.Get("type", "wav").String()
	}()

	fnLanguage := func() string {
		if !g.IsEmpty(req.Language) {
			return req.Language
		}
		return jsParams.Get("language", "zh-TW").String()
	}()
	fnSpeaker := func() string {
		return jsParams.Get("speaker", "zh-TW-HsiaoChenNeural").String()
	}()

	fnSpeed := func() string {
		if !g.IsEmpty(req.Speed) {
			return gconv.String(req.Speed)
		}
		return jsParams.Get("speed", "default").String()
	}()
	fnVolume := func() string {
		if !g.IsEmpty(req.Volume) {
			return gconv.String(req.Volume)
		}
		return jsParams.Get("volume", "default").String()
	}()
	fnPitch := func() string {
		return jsParams.Get("pitch", "default").String()
	}()
	fnDelFileOrNot := func(f string) bool {
		bSave, _ := g.Cfg().Get(ctx, CfgKeyTTS+".save_voice_file", true)
		if !bSave.Bool() {
			if gfile.Exists(f) {
				_ = gfile.Remove(f)
			}
		}

		return bSave.Bool()
	}

	vPath, _ := g.Cfg().Get(ctx, CfgKeyTTS+".save_folder", "./voc")
	fileName := fmt.Sprintf("%s.%s", gtime.TimestampMicroStr(), fnType)
	dir := gfile.Join(vPath.String(), gtime.Now().Format("Y-m-d"))
	if !gfile.Exists(dir) {
		_ = gfile.Mkdir(dir)
	}

	fullName := gfile.Join(dir, fileName)

	g.Log().Cat(DEBUG).Cat(AZ).Debugf(ctx, "The tts output file:%s", fullName)

	audioConfig, err := audio.NewAudioConfigFromWavFileOutput(fullName)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return nil, err
	}
	defer audioConfig.Close()

	speechConfig, err := speech.NewSpeechConfigFromSubscription(key, region)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return nil, err
	}
	defer speechConfig.Close()
	err = speechConfig.SetPropertyByString("OPENSSL_DISABLE_CRL_CHECK", "true")
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return nil, err
	}

	// speechConfig.properties.SetPropertyByString("OPENSSL_CONTINUE_ON_CRL_DOWNLOAD_FAILURE", "true")
	// speechConfig.SetProperty()
	speechSynthesizer, err := speech.NewSpeechSynthesizerFromConfig(speechConfig, audioConfig)
	if err != nil {
		g.Log().Cat(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return nil, err
	}
	defer speechSynthesizer.Close()

	mRepValues := g.MapStrStr{
		LanguagePlaceholder: fnLanguage,
		SpeakerPlaceholder:  fnSpeaker,
		SpeedPlaceholder:    fnSpeed,
		VolumePlaceholder:   fnVolume,
		PitchPlaceholder:    fnPitch,
	}
	strFmt := gstr.ReplaceIByMap(vPattern.String(), mRepValues)
	ssmlData := fmt.Sprintf(strFmt, req.Text)
	g.Log().Cat(DEBUG).Cat(AZ).Debugf(ctx, "SSML: %s", ssmlData)
	vTimeout, _ := g.Cfg().Get(ctx, CfgKeyTTS+".action_ack_timeout", "1m")
	startTime := gtime.Now()

	speechSynthesizer.SynthesisStarted(func(event speech.SpeechSynthesisEventArgs) {
		defer event.Close()
		g.Log().Cat(DEBUG).Cat(AZ).Debug(ctx, "Synthesis started.")
	})
	speechSynthesizer.Synthesizing(func(event speech.SpeechSynthesisEventArgs) {
		defer event.Close()
		//g.Log().Cat(DEBUG).Cat(AZ).Debugf(ctx, "Synthesizing, audio chunk size %d", len(event.Result.AudioData))
	})
	speechSynthesizer.SynthesisCompleted(func(event speech.SpeechSynthesisEventArgs) {
		defer event.Close()
		dur := gtime.Now().Sub(startTime)
		service.SetNodeCost(ctx, az.profileKey, key, gconv.Int(dur.Seconds()))
		g.Log().Cat(DEBUG).Cat(AZ).Debugf(ctx, "Synthesized, audio length %d  ,duration : %v", len(event.Result.AudioData), event.Result.AudioDuration)

	})
	speechSynthesizer.SynthesisCanceled(func(event speech.SpeechSynthesisEventArgs) {
		defer event.Close()
		g.Log().Cat(DEBUG).Cat(AZ).Debug(ctx, "Received a cancellation")
	})

	task := speechSynthesizer.SpeakSsmlAsync(ssmlData)

	var outcome speech.SpeechSynthesisOutcome

	select {
	case outcome = <-task:
	case <-time.After(vTimeout.Duration()):
		err = gerror.NewCode(ErrorGeneralError, "Time out ...")
		g.Log().Cat(ERROR).Error(ctx, err)
		return nil, err
	}

	defer outcome.Close()

	if outcome.Error != nil {
		g.Log().Cat(ERROR).Error(ctx, outcome.Error)
		err = gerror.WrapCode(ErrorGeneralError, outcome.Error)
		return
	}
	if outcome.Result.Reason == common.SynthesizingAudioCompleted {

		// write audio data and return
		ret.Buf = outcome.Result.AudioData
		if fnDelFileOrNot(fullName) {
			// if save flag is true then set filename and ext name
			ret.FileName = gfile.Basename(fullName)
			ret.Ext = gfile.ExtName(fullName)
			// save to memory
			_ = gcache.Set(ctx, req.Text, fullName, gcache.DurationNoExpire)
		}

		res = ret

		return

	} else {
		cancellation, _ := speech.NewCancellationDetailsFromSpeechSynthesisResult(outcome.Result)
		g.Log().Cat(DEBUG).Cat(AZ).Debugf(ctx, " Canceled - reason = %v", cancellation.Reason)
		if cancellation.Reason == common.Error {
			err = gerror.NewCodef(ErrorGeneralError, "Error code = %v ,Error detail=%v", cancellation.ErrorCode, cancellation.ErrorDetails)
			g.Log().Cat(ERROR).Error(ctx, err)
			return
		}
	}

	return
}
