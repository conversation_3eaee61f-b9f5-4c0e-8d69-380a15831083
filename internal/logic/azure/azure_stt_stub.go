//go:build !azure

package azure

import (
	"context"
	"fmt"

	. "SonaMesh/internal/consts"
	"SonaMesh/internal/model"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// STT 的 stub 實現，當不包含 azure tag 時使用
type STT struct {
	ackFunc    AckFunc
	resultFunc ResultFunc
	profileKey string
}

// NewAzureSTT 創建 Azure STT 的 stub 實現
func NewAzureSTT(ackHandler AckFunc, resultHandler ResultFunc, profileKey string) (*STT, error) {
	o := &STT{
		ackFunc:    ackHandler,
		resultFunc: resultHandler,
	}
	if g.<PERSON>(profileKey) {
		o.profileKey = `vendor.Azure.STT`
	} else {
		o.profileKey = fmt.Sprintf(CfgKeyAzureSTT, profileKey)
	}

	// 在 stub 實現中，我們不檢查配置，因為 Azure 功能不可用
	g.Log().Cat(INFO).Infof(context.TODO(), "Azure STT is not supported in this build (compiled without azure tag)")

	return o, nil
}

// Connect 的 stub 實現
func (az *STT) Connect(ctx context.Context) (err error) {
	err = gerror.NewCode(ErrorGeneralError, "Azure STT is not supported in this build")
	g.Log().Cat(ERROR).Error(ctx, err)
	return err
}

// Start 的 stub 實現
func (az *STT) Start(ctx context.Context, params any) (err error) {
	err = gerror.NewCode(ErrorGeneralError, "Azure STT is not supported in this build")
	g.Log().Cat(ERROR).Error(ctx, err)

	// 發送錯誤 ACK
	if az.ackFunc != nil {
		ack := &model.AckRes{
			Message: struct {
				Type string `json:"type"`
			}(struct{ Type string }{Type: "ack"}),
			Ack:    ActionStart,
			Status: ErrorGeneralError.Code(),
		}
		az.ackFunc(ctx, ack)
	}

	return err
}

// Stop 的 stub 實現
func (az *STT) Stop(ctx context.Context, params any) (err error) {
	err = gerror.NewCode(ErrorGeneralError, "Azure STT is not supported in this build")
	g.Log().Cat(ERROR).Error(ctx, err)

	// 發送停止 ACK
	if az.ackFunc != nil {
		ack := &model.AckRes{
			Message: struct {
				Type string `json:"type"`
			}(struct{ Type string }{Type: "ack"}),
			Ack:    ActionStop,
			Status: ErrorGeneralError.Code(),
		}
		az.ackFunc(ctx, ack)
	}

	return err
}

// SendVoiceBuffer 的 stub 實現
func (az *STT) SendVoiceBuffer(ctx context.Context, buf []byte) (err error) {
	err = gerror.NewCode(ErrorGeneralError, "Azure STT is not supported in this build")
	g.Log().Cat(ERROR).Error(ctx, err)
	return err
}

// RecognizeFile 的 stub 實現
func (az *STT) RecognizeFile(ctx context.Context, params *model.STTParams) (res any, err error) {
	err = gerror.NewCode(ErrorGeneralError, "Azure STT is not supported in this build")
	g.Log().Cat(ERROR).Error(ctx, err)
	return nil, err
}

// Interrupt 的 stub 實現
func (az *STT) Interrupt(ctx context.Context) {
	g.Log().Cat(INFO).Info(ctx, "Azure STT interrupt called, but Azure STT is not supported in this build")
}
