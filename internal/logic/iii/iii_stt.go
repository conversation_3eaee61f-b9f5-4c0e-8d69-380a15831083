package iii

import (
	. "SonaMesh/internal/consts"
	"SonaMesh/internal/model"
	"SonaMesh/utility"
	"context"
	"crypto/tls"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gorilla/websocket"
)

type STT struct {
	ackFunc        AckFunc
	resultFunc     ResultFunc
	readerPoll     *grpool.Pool
	conn           *websocket.Conn
	isInterrupt    bool
	isShortCommand bool
	chShortCommand chan *model.ShortCommand
	isProactive    bool
	profileKey     string
	role           string
	userNumber     string
}

func NewIIISTT(ackHandler AckFunc, resultHandler ResultFunc, profileKey string) (*STT, error) {
	o := &STT{
		readerPoll: grpool.New(),
		ackFunc:    ackHandler,
		resultFunc: resultHandler,
		profileKey: `vendor.III.STT`,
		role:       "unknown",
	}
	if !g.IsEmpty(profileKey) {
		o.profileKey = fmt.Sprintf(CfgKeyIIIBotSTT, profileKey)
	}
	v, err := g.Cfg().Get(context.TODO(), o.profileKey)
	if err != nil {
		return nil, err
	}
	if v.IsEmpty() {
		return nil, gerror.Newf("please check the configuration , key %q ", o.profileKey)
	}
	return o, nil
}
func (s *STT) logger(cat string) glog.ILogger {
	return g.Log().Cat(cat).Cat(III)
}
func (s *STT) Connect(ctx context.Context) (err error) {

	s.logger(INFO).Info(ctx, "Connect to host ")
	roleDef := gconv.String(ctx.Value("role"))
	urlKey := s.profileKey + ".url_speechless_person_recognition"
	if g.IsEmpty(roleDef) {
		urlKey = s.profileKey + ".url_speech_recognition"
	}
	vUrl, _ := g.Cfg().Get(ctx, urlKey)
	if vUrl.IsEmpty() {
		err = gerror.NewCode(ErrorGeneralError, "The stt url is  empty")
		s.logger(ERROR).Error(ctx, err)
		return
	}

	vHandShakeTimeout, _ := g.Cfg().Get(ctx, "websocket.hand_shake_timeout", "45s")
	c := gclient.NewWebSocket()
	c.Proxy = nil
	c.HandshakeTimeout = vHandShakeTimeout.Duration()

	// 從配置文件讀取 TLS 設置，預設為安全模式
	vInsecureSkipVerify, _ := g.Cfg().Get(ctx, s.profileKey+".tls.insecure_skip_verify", false)
	vMinVersion, _ := g.Cfg().Get(ctx, s.profileKey+".tls.min_version", "1.2")

	// 設置 TLS 最小版本
	minVersion := tls.VersionTLS12
	switch vMinVersion.String() {
	case "1.0":
		minVersion = tls.VersionTLS10
	case "1.1":
		minVersion = tls.VersionTLS11
	case "1.2":
		minVersion = tls.VersionTLS12
	case "1.3":
		minVersion = tls.VersionTLS13
	}

	c.TLSClientConfig = &tls.Config{
		InsecureSkipVerify: vInsecureSkipVerify.Bool(),
		MinVersion:         uint16(minVersion),
	}

	s.conn, _, err = c.Dial(vUrl.String(), nil)
	if err != nil {
		s.logger(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}
	s.logger(INFO).Info(ctx, "Connect to host success")
	s.conn.SetCloseHandler(func(code int, text string) error {
		s.logger(DEBUG).Debugf(ctx, "Close code : %d , text : %s", code, text)
		return nil
	})

	if err = s.readerPoll.AddWithRecover(ctx, s.onMessage, func(ctx context.Context, exception error) {
		s.logger(DEBUG).Debug(ctx, exception)
	}); err != nil {
		s.logger(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}

	return
}

func (s *STT) onMessage(ctx context.Context) {
	s.logger(DEBUG).Debug(ctx, "Start receive message...")
	defer func() {
		s.logger(DEBUG).Debug(ctx, "End receive message...")
		if s.conn != nil {
			_ = s.conn.Close()
			s.isProactive, s.isInterrupt, s.isShortCommand = false, false, false
		}

	}()
	if s.conn != nil {
		for {
			var data *gjson.Json
			if err := s.conn.ReadJSON(&data); err != nil {
				s.logger(DEBUG).Debug(ctx, err)
				return
			}
			if s.isInterrupt {
				s.logger(DEBUG).Debug(ctx, "Interrupted...")
				return
			}
			s.logger(DEBUG).Debugf(ctx, "Receive message : %v", gjson.New(data).MustToJsonIndentString())
			if data != nil {
				retResultStr := strings.Builder{}
				redisRecords := make([]*model.Record, 0)

				fnCreateRedisRecord := func(regText, speaker, begTimeStamp, endTimeStamp string) {
					redisRecord := &model.Record{
						Timestamp:        gtime.Now().TimestampMicroStr(),
						Text:             regText,
						BufferTranscript: "",
						Speaker:          speaker,
						BegTimeStamp:     begTimeStamp,
						EndTimeStamp:     endTimeStamp,
						UserNumber:       s.userNumber,
					}
					redisRecords = append(redisRecords, redisRecord)
					retResultStr.WriteString(fmt.Sprintf("%s ( %s - %s ):%s\n", speaker, begTimeStamp, endTimeStamp, regText))
				}

				if !g.IsEmpty(s.role) {

					//read lines.0
					key := "lines.0."
					speaker := s.role
					regText := data.Get(key + "text").String()
					beg := data.Get(key + "beg").String()
					end := data.Get(key + "end").String()
					fnCreateRedisRecord(regText, speaker, beg, end)

				} else {

					for _, jsData := range data.GetJsons("lines") {
						speaker := jsData.Get("speaker").String()
						regText := jsData.Get("text").String()
						beg := jsData.Get("beg").String()
						end := jsData.Get("end").String()
						if !g.IsEmpty(gstr.Trim(regText)) {
							fnCreateRedisRecord(regText, speaker, beg, end)
						}
					}
				}

				result := &model.ResultRes{}
				result.Result.Transcript = retResultStr.String()
				result.Message.Type = "result"
				result.Status = ErrorOK.Code()
				result.Final = true
				result.Result.Likelihood = 1.0

				regResult := &model.RegResult{
					ToRedis: redisRecords,
					ToSIPX:  result,
				}

				if s.resultFunc != nil && s.isInterrupt == false && s.isShortCommand == false {
					s.resultFunc(ctx, regResult)
				}

				if s.isShortCommand {
					s.chShortCommand <- &model.ShortCommand{Text: result.Result.Transcript}
				}

			}

		}
	}
}

func (s *STT) Start(ctx context.Context, params any) (err error) {
	s.logger(INFO).Infof(ctx, "Start: %v", gjson.New(params).MustToJsonIndentString())

	fnReturnStartAck := func(statusCode int) {
		ack := &model.AckRes{
			Message: struct {
				Type string `json:"type"`
			}(struct{ Type string }{Type: "ack"}),
			Ack:    "start",
			Status: statusCode,
		}
		if s.ackFunc != nil {
			s.ackFunc(ctx, ack)
		}
	}

	if s.conn == nil {
		err = gerror.NewCode(ErrorGeneralError, "Not  connect to the host...")
		s.logger(ERROR).Error(ctx, err)
		fnReturnStartAck(ErrorGeneralError.Code())
		return
	}
	var req *model.SttStartReq
	_ = gconv.Scan(params, &req)
	if req == nil {
		err = gerror.NewCode(ErrorGeneralError, "The stt parameters is  empty")
		s.logger(ERROR).Error(ctx, err)
		fnReturnStartAck(ErrorGeneralError.Code())
	}
	s.role = req.Role
	if !g.IsEmpty(gstr.Trim(req.Params)) {
		kv := utility.SplitParamsToMap(req.Params)
		if len(kv) > 0 {
			// 固定 key 為'userNumber'
			if v, ok := kv["userNumber"]; ok {
				s.userNumber = gconv.String(v)
			}
		}
	}

	s.isShortCommand = req.ShortCommand
	if s.isShortCommand {
		s.chShortCommand = make(chan *model.ShortCommand, 1)
		_ = s.readerPoll.AddWithRecover(
			gctx.NeverDone(ctx),
			s.waitShortCommandRecong,
			func(ctx context.Context, exception error) {
				s.logger(ERROR).Error(ctx, exception)
			},
		)

	}

	fnReturnStartAck(ErrorOK.Code())
	return
}

func (s *STT) Stop(ctx context.Context, params any) (err error) {
	s.logger(INFO).Infof(ctx, "Stop: %v", gjson.New(params).MustToJsonIndentString())
	fnReturnStopAck := func(statusCode int) {
		ack := &model.AckRes{
			Message: struct {
				Type string `json:"type"`
			}(struct{ Type string }{Type: "ack"}),
			Ack:    "stop",
			Status: statusCode,
		}
		if s.ackFunc != nil {
			s.ackFunc(ctx, ack)
		}
	}

	if s.conn == nil {
		err = gerror.NewCode(ErrorGeneralError, "Not  connect to the host...")
		s.logger(ERROR).Error(ctx, err)
		fnReturnStopAck(ErrorGeneralError.Code())
		return
	}
	e := s.conn.Close()
	if e != nil {
		s.logger(DEBUG).Debug(ctx, e)
	}
	s.isProactive = true
	s.conn = nil
	fnReturnStopAck(ErrorOK.Code())
	return
}

func (s *STT) SendVoiceBuffer(ctx context.Context, buf []byte) (err error) {

	if s.conn == nil {
		err = gerror.NewCode(ErrorGeneralError, "Not  connect to the host...")
		s.logger(ERROR).Error(ctx, err)
		return
	}

	err = s.conn.WriteMessage(websocket.BinaryMessage, buf)
	if err != nil {
		err = gerror.WrapCode(ErrorGeneralError, err)
		s.logger(ERROR).Error(ctx, err)
	}

	return
}

func (s *STT) RecognizeFile(ctx context.Context, params *model.STTParams) (res any, err error) {
	s.logger(INFO).Infof(ctx, "RecognizeFile: %v", gjson.New(params).MustToJsonIndentString())

	vUrl, _ := g.Cfg().Get(ctx, s.profileKey+".http_url", "")
	if vUrl.IsEmpty() {
		err = gerror.NewCode(ErrorGeneralError, "The stt url is  empty")
		s.logger(ERROR).Error(ctx, err)
		return
	}
	if !gfile.Exists(params.FileName) {
		err = gerror.NewCode(ErrorGeneralError, "The file is not exist")
		s.logger(ERROR).Error(ctx, err)
		return
	}
	postData := fmt.Sprintf("file=@file:%s", params.FileName)
	url := vUrl.String() + fmt.Sprintf("?translate=%v", params.Translate)
	s.logger(DEBUG).Debugf(ctx, "RecognizeFile: url[%s] , data [%v] ", url, postData)
	resp, err := g.Client().Post(ctx, url, postData)
	if err != nil {
		s.logger(ERROR).Error(ctx, err)
		err = gerror.WrapCode(ErrorGeneralError, err)
		return
	}
	defer resp.Close()
	r := resp.ReadAllString()
	if !gjson.Valid(r) {
		s.logger(DEBUG).Debugf(ctx, "RecognizeFile: %v", r)
		err = gerror.NewCode(ErrorGeneralError)
	} else {

		jsResult := gjson.New(r)
		s.logger(DEBUG).Debugf(ctx, "RecognizeFile: %v", jsResult.MustToJsonIndentString())
		sb := strings.Builder{}
		for _, sentence := range jsResult.GetJsons("all_dict.segments") {
			sb.WriteString(fmt.Sprintf(
				"%s  (%v-%v) translation (%s): %s \n",
				sentence.Get("speaker").String(),
				sentence.Get("beg").Float32(),
				sentence.Get("end").Float32(),
				sentence.Get("translation").String(),
				sentence.Get("text").String(),
			))

		}

		res = sb.String()

	}

	return

}

func (s *STT) Interrupt(ctx context.Context) {
	s.logger(INFO).Info(ctx, "Interrupt...")
	s.isInterrupt = true
	if s.conn != nil {
		_ = s.conn.Close()
		s.conn = nil
	}
}

func (s *STT) waitShortCommandRecong(ctx context.Context) {

	s.logger(INFO).Info(ctx, "waitShortCommandRecong...")
	vTimeout, _ := g.Cfg().Get(ctx, CfgKeySTT+".short_command_recog_timeout", "5s")
	fnFinish := func() {
		ack := &model.AckRes{}
		ack.Message.Type = "ack"
		ack.Status = ErrorState.Code()
		ack.Ack = ActionFinish
		if s.ackFunc != nil {
			s.ackFunc(ctx, ack)
		}
	}
	fnResult := func(r string) {
		result := &model.ResultRes{}
		result.Message.Type = "result"
		result.Result.Likelihood = 1.0
		result.Result.Transcript = r
		result.Final = true
		result.Status = ErrorOK.Code()
		if s.resultFunc != nil {
			s.resultFunc(ctx, result)
		}
	}
	var r *model.ShortCommand
	g.Log().Cat(DEBUG).Cat(EM).Debug(ctx, "Wait recognize result ... ")
	select {
	case r = <-s.chShortCommand:
	case <-time.After(vTimeout.Duration()):
		g.Log().Cat(DEBUG).Cat(EM).Debug(ctx, "Identify short command timeout")
		fnFinish()

		//if s.conn != nil {
		//	_ = s.conn.WriteJSON(gjson.New(EmotiStopCommand))
		//}

		return

	}

	s.logger(DEBUG).Debugf(ctx, "Recognized Results Of Short Instructions:%s", r.Text)
	fnResult(r.Text)

	//if em.conn != nil {
	//	_ = em.conn.WriteJSON(gjson.New(EmotiStopCommand))
	//}

}
